import React, { useState, useRef, useEffect } from 'react';
import { Download, Settings, Wand2, Image as ImageIcon, Loader2, Upload, X } from 'lucide-react';
import axios from 'axios';

const ASPECT_RATIOS = [
  { value: '3:7', label: '3:7 (Portrait)' },
  { value: '4:7', label: '4:7' },
  { value: '9:16', label: '9:16' },
  { value: '2:3', label: '2:3' },
  { value: '1:1', label: '1:1 (Square)' },
  { value: '3:2', label: '3:2' },
  { value: '16:9', label: '16:9' },
  { value: '7:4', label: '7:4' },
  { value: '7:3', label: '7:3 (Landscape)' }
];

const SAFETY_LEVELS = [
  { value: 0, label: '0 - Most Strict' },
  { value: 1, label: '1 - Very Strict' },
  { value: 2, label: '2 - Strict (Default)' },
  { value: 3, label: '3 - Moderate' },
  { value: 4, label: '4 - Permissive' },
  { value: 5, label: '5 - More Permissive' },
  { value: 6, label: '6 - Most Permissive' }
];

const SAFETY_LEVELS_IMAGE_EDIT = [
  { value: 0, label: '0 - Most Strict' },
  { value: 1, label: '1 - Very Strict' },
  { value: 2, label: '2 - Balanced (Default)' }
];

const MODELS = [
  {
    value: 'flux-kontext-pro',
    label: 'FLUX.1 Kontext Pro',
    description: 'Best for text-to-image and image editing with reference images',
    supportsImageInput: true,
    supportsImagePrompt: false
  },
  {
    value: 'flux-pro-1.1',
    label: 'FLUX.1.1 Pro',
    description: 'Latest high-quality text-to-image model (text prompts only)',
    supportsImageInput: false,
    supportsImagePrompt: true
  },
  {
    value: 'flux-pro',
    label: 'FLUX.1 Pro',
    description: 'High-quality text-to-image generation (text prompts only)',
    supportsImageInput: false,
    supportsImagePrompt: true
  },
  {
    value: 'flux-dev',
    label: 'FLUX.1 Dev',
    description: 'Development model for testing (text prompts only)',
    supportsImageInput: false,
    supportsImagePrompt: true
  }
];

export default function ImageGenerator() {
  const [prompt, setPrompt] = useState('');
  const [model, setModel] = useState('flux-kontext-pro');
  const [inputImage, setInputImage] = useState(null);
  const [inputImagePreview, setInputImagePreview] = useState(null);
  // Multi-reference images (new feature)
  const [inputImage2, setInputImage2] = useState(null);
  const [inputImagePreview2, setInputImagePreview2] = useState(null);
  const [inputImage3, setInputImage3] = useState(null);
  const [inputImagePreview3, setInputImagePreview3] = useState(null);
  const [inputImage4, setInputImage4] = useState(null);
  const [inputImagePreview4, setInputImagePreview4] = useState(null);

  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [seed, setSeed] = useState('');
  const [promptUpsampling, setPromptUpsampling] = useState(false);
  const [safetyTolerance, setSafetyTolerance] = useState(2);
  const [outputFormat, setOutputFormat] = useState('jpeg');
  const [batchCount, setBatchCount] = useState(1);
  const [autoSave, setAutoSave] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const fileInputRef = useRef(null);
  const fileInputRef2 = useRef(null);
  const fileInputRef3 = useRef(null);
  const fileInputRef4 = useRef(null);

  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState([]);
  const [currentStatus, setCurrentStatus] = useState('');
  const [batchProgress, setBatchProgress] = useState({ current: 0, total: 0 });
  const [selectedImage, setSelectedImage] = useState(null);

  // Get selected model info
  const selectedModel = MODELS.find(m => m.value === model);

  // Debug: Log current model state
  console.log('Current model state:', { model, selectedModel: selectedModel?.label });

  // Get appropriate safety levels based on whether we have input image
  const availableSafetyLevels = (inputImage && selectedModel?.supportsImageInput)
    ? SAFETY_LEVELS_IMAGE_EDIT
    : SAFETY_LEVELS;

  // Clear input images when switching to a model that doesn't support them
  useEffect(() => {
    if (!selectedModel?.supportsImageInput && (inputImage || inputImage2 || inputImage3 || inputImage4)) {
      setInputImage(null);
      setInputImagePreview(null);
      setInputImage2(null);
      setInputImagePreview2(null);
      setInputImage3(null);
      setInputImagePreview3(null);
      setInputImage4(null);
      setInputImagePreview4(null);
    }
  }, [model, selectedModel?.supportsImageInput, inputImage, inputImage2, inputImage3, inputImage4]);



  // Generic image upload handler
  const handleImageUpload = (event, imageIndex = 1) => {
    const file = event.target.files[0];
    if (file) {
      // Check file size (max 20MB as per BFL API)
      if (file.size > 20 * 1024 * 1024) {
        alert('File size must be less than 20MB (BFL API limit)');
        return;
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const base64String = e.target.result.split(',')[1]; // Remove data:image/...;base64, prefix

        // Additional check for base64 size (roughly 4/3 of original file size)
        const base64Size = base64String.length * 0.75; // Approximate original size
        console.log(`Image ${imageIndex} file size: ${(file.size / 1024 / 1024).toFixed(2)}MB, Base64 size: ${(base64Size / 1024 / 1024).toFixed(2)}MB`);

        // Set the appropriate image state based on index
        switch (imageIndex) {
          case 1:
            setInputImage(base64String);
            setInputImagePreview(e.target.result);
            break;
          case 2:
            setInputImage2(base64String);
            setInputImagePreview2(e.target.result);
            break;
          case 3:
            setInputImage3(base64String);
            setInputImagePreview3(e.target.result);
            break;
          case 4:
            setInputImage4(base64String);
            setInputImagePreview4(e.target.result);
            break;
        }

        // Adjust safety tolerance if it's too high for image editing
        if (safetyTolerance > 2) {
          setSafetyTolerance(2);
        }
      };

      reader.onerror = () => {
        alert('Error reading file. Please try again.');
      };

      reader.readAsDataURL(file);
    }
  };

  // Remove uploaded image
  const removeImage = (imageIndex = 1) => {
    switch (imageIndex) {
      case 1:
        setInputImage(null);
        setInputImagePreview(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        break;
      case 2:
        setInputImage2(null);
        setInputImagePreview2(null);
        if (fileInputRef2.current) {
          fileInputRef2.current.value = '';
        }
        break;
      case 3:
        setInputImage3(null);
        setInputImagePreview3(null);
        if (fileInputRef3.current) {
          fileInputRef3.current.value = '';
        }
        break;
      case 4:
        setInputImage4(null);
        setInputImagePreview4(null);
        if (fileInputRef4.current) {
          fileInputRef4.current.value = '';
        }
        break;
    }
    // Reset safety tolerance to appropriate default
    setSafetyTolerance(2);
  };

  // Generate single image
  const generateSingleImage = async (batchIndex = 0) => {
    const requestData = {
      prompt: prompt.trim(),
      model: model,
      aspect_ratio: aspectRatio,
      prompt_upsampling: promptUpsampling,
      safety_tolerance: safetyTolerance,
      output_format: outputFormat
    };

    // Debug: Log the request data
    console.log('Frontend Request Data:', requestData);

    // Add input image if available and model supports it
    if (inputImage && selectedModel?.supportsImageInput) {
      requestData.input_image = inputImage;
    }

    // Use different seeds for batch generation (unless user specified one)
    if (seed.trim()) {
      requestData.seed = parseInt(seed) + batchIndex;
    }

    const response = await axios.post('/api/generate', requestData);
    const { id, polling_url } = response.data;

    // Poll for result
    let attempts = 0;
    const maxAttempts = 120;

    const pollResult = async () => {
      try {
        const pollResponse = await axios.get('/api/poll', {
          params: { polling_url }
        });

        const { status, result } = pollResponse.data;

        if (batchCount > 1) {
          setCurrentStatus(`Batch ${batchIndex + 1}/${batchCount} - Status: ${status}`);
        } else {
          setCurrentStatus(`Status: ${status}`);
        }

        if (status === 'Ready' && result?.sample) {
          // Debug: Log the complete result
          console.log('Poll result debug:', {
            status: status,
            result: result,
            imageUrl: result.sample,
            aspectRatio: aspectRatio,
            requestedAspectRatio: aspectRatio
          });

          const newImage = {
            id,
            url: result.sample,
            prompt,
            model,
            aspectRatio,
            seed: seed ? (parseInt(seed) + batchIndex).toString() : 'Random',
            hasInputImage: !!inputImage,
            timestamp: new Date().toLocaleString(),
            batchIndex: batchIndex + 1
          };

          // Auto-save if enabled
          await autoSaveImage(result.sample, newImage);

          setGeneratedImages(prev => [newImage, ...prev]);
          return newImage;
        }

        if (status === 'Error' || status === 'Failed') {
          throw new Error(`Generation failed: ${status}`);
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(pollResult, 1000);
        } else {
          throw new Error('Timeout: Generation took too long');
        }
      } catch (error) {
        console.error('Polling error:', error);
        throw error;
      }
    };

    return pollResult();
  };

  const generateImage = async () => {
    if (!prompt.trim()) {
      alert('Please enter a prompt');
      return;
    }

    setIsGenerating(true);
    setBatchProgress({ current: 0, total: batchCount });
    setCurrentStatus('Starting generation...');

    try {
      const results = [];

      // Generate images in sequence (to avoid API rate limits)
      for (let i = 0; i < batchCount; i++) {
        setBatchProgress({ current: i, total: batchCount });

        try {
          const result = await generateSingleImage(i);
          results.push(result);
        } catch (error) {
          console.error(`Batch ${i + 1} failed:`, error);

          // Handle specific error types with user-friendly messages
          let errorMessage = error.message;
          if (error.response?.status === 413) {
            errorMessage = 'Image file too large. Please use a smaller image (max 20MB).';
          } else if (error.response?.status === 400) {
            errorMessage = `API Error: ${error.response.data?.error || 'Invalid request parameters'}`;
          } else if (error.response?.status === 401) {
            errorMessage = 'API key invalid or expired';
          } else if (error.response?.status === 429) {
            errorMessage = 'Rate limit exceeded. Please wait before trying again.';
          } else if (error.code === 'ECONNABORTED') {
            errorMessage = 'Request timeout. Please try again.';
          }

          setCurrentStatus(`Batch ${i + 1}/${batchCount} failed: ${errorMessage}`);

          // Stop batch for critical errors
          if (error.response?.status === 413 || error.response?.status === 401) {
            break;
          }
          // Continue with next image for other errors
        }
      }

      setIsGenerating(false);
      setBatchProgress({ current: 0, total: 0 });
      setCurrentStatus(`Completed! Generated ${results.length}/${batchCount} images.`);

      // Clear status after 3 seconds
      setTimeout(() => setCurrentStatus(''), 3000);

    } catch (error) {
      console.error('Generation error:', error);

      // Handle specific error types
      let errorMessage = error.message;
      if (error.response?.status === 413) {
        errorMessage = 'Request too large. Please use a smaller image file.';
      } else if (error.response?.status === 400) {
        errorMessage = `API Error: ${error.response.data?.error || 'Invalid request parameters'}`;
      } else if (error.response?.status === 401) {
        errorMessage = 'API key invalid or expired';
      } else if (error.response?.status === 429) {
        errorMessage = 'Rate limit exceeded. Please wait before trying again.';
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timeout. Please try again.';
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      setCurrentStatus(`Error: ${errorMessage}`);
      setIsGenerating(false);
      setBatchProgress({ current: 0, total: 0 });
    }
  };

  // Auto-save to downloads folder
  const autoSaveImage = async (imageUrl, imageData) => {
    if (!autoSave) return;

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      const filename = `flux-${timestamp}-${imageData.model}-${imageData.id}.${outputFormat}`;
      await downloadImage(imageUrl, filename);
    } catch (error) {
      console.error('Auto-save error:', error);
    }
  };

  const downloadImage = async (imageUrl, filename) => {
    try {
      // Use our proxy endpoint to download the image
      const downloadUrl = `/api/download-image?url=${encodeURIComponent(imageUrl)}&filename=${encodeURIComponent(filename || 'flux-image.jpg')}`;

      // Create a temporary link and trigger download
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename || 'flux-image.jpg';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

    } catch (error) {
      console.error('Download error:', error);
      // Fallback: open in new tab
      window.open(imageUrl, '_blank');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          FLUX.1 Image Generator
        </h1>
        <p className="text-gray-600">
          Generate high-quality AI images with FLUX models
        </p>


      </div>

      {/* Generation Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="space-y-6">
          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={model}
              onChange={(e) => {
                console.log('Model changing from', model, 'to', e.target.value);
                setModel(e.target.value);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isGenerating}
            >
              {MODELS.map(modelOption => (
                <option key={modelOption.value} value={modelOption.value}>
                  {modelOption.label}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">
              {selectedModel?.description}
            </p>
          </div>

          {/* Reference Image Upload (only for Kontext models) */}
          {selectedModel?.supportsImageInput ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reference Image (Optional)
              </label>

              {!inputImagePreview ? (
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-400 transition-colors"
                >
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600">
                    Click to upload reference image
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Max 20MB • JPEG, PNG, WebP
                  </p>
                </div>
              ) : (
                <div className="relative">
                  <img
                    src={inputImagePreview}
                    alt="Reference"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    onClick={removeImage}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    disabled={isGenerating}
                  >
                    <X size={16} />
                  </button>
                </div>
              )}

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={isGenerating}
              />

              <p className="text-xs text-gray-500 mt-2">
                Use reference images for prompts like "same woman but with red hair" or "change the car color to blue"
              </p>
            </div>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <div className="text-yellow-600 mt-0.5">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Reference Images Not Supported</h4>
                  <p className="text-xs text-yellow-700 mt-1">
                    {selectedModel?.label} only supports text-to-image generation.
                    For image-to-image editing with reference images, please use <strong>FLUX.1 Kontext Pro</strong>.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Prompt Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={
                inputImage
                  ? "Describe what you want to change in the reference image..."
                  : "Describe the image you want to generate..."
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 h-24 resize-none"
              disabled={isGenerating}
            />
          </div>

          {/* Basic Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Aspect Ratio
              </label>
              <select
                value={aspectRatio}
                onChange={(e) => setAspectRatio(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isGenerating}
              >
                {ASPECT_RATIOS.map(ratio => (
                  <option key={ratio.value} value={ratio.value}>
                    {ratio.label}
                  </option>
                ))}
              </select>
              {model !== 'flux-kontext-pro' && (
                <p className="text-xs text-gray-500 mt-1">
                  Note: {selectedModel?.label} uses width/height conversion from aspect ratio
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Batch Count
              </label>
              <select
                value={batchCount}
                onChange={(e) => setBatchCount(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isGenerating}
              >
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(count => (
                  <option key={count} value={count}>
                    {count} image{count > 1 ? 's' : ''}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Output Format
              </label>
              <select
                value={outputFormat}
                onChange={(e) => setOutputFormat(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isGenerating}
              >
                <option value="jpeg">JPEG</option>
                <option value="png">PNG</option>
              </select>
            </div>
          </div>

          {/* Auto-save Option */}
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="autoSave"
              checked={autoSave}
              onChange={(e) => setAutoSave(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              disabled={isGenerating}
            />
            <label htmlFor="autoSave" className="text-sm text-gray-700">
              Auto-save images to Downloads folder
            </label>
            <span className="text-xs text-gray-500">
              (Automatically download generated images)
            </span>
          </div>

          {/* Advanced Settings Toggle */}
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium"
          >
            <Settings size={16} />
            {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
          </button>

          {/* Advanced Settings */}
          {showAdvanced && (
            <div className="border-t pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Seed (Optional)
                  </label>
                  <input
                    type="number"
                    value={seed}
                    onChange={(e) => setSeed(e.target.value)}
                    placeholder="Leave empty for random"
                    className="input-field"
                    disabled={isGenerating}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use same seed for reproducible results
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Safety Tolerance
                  </label>
                  <select
                    value={safetyTolerance}
                    onChange={(e) => setSafetyTolerance(parseInt(e.target.value))}
                    className="select-field"
                    disabled={isGenerating}
                  >
                    {availableSafetyLevels.map(level => (
                      <option key={level.value} value={level.value}>
                        {level.label}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    {inputImage && selectedModel?.supportsImageInput
                      ? 'Image editing supports levels 0-2'
                      : 'Text-to-image supports levels 0-6'
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="promptUpsampling"
                  checked={promptUpsampling}
                  onChange={(e) => setPromptUpsampling(e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  disabled={isGenerating}
                />
                <label htmlFor="promptUpsampling" className="text-sm text-gray-700">
                  Enable Prompt Upsampling
                </label>
                <span className="text-xs text-gray-500">
                  (Improves prompt quality)
                </span>
              </div>
            </div>
          )}

          {/* Generate Button */}
          <button
            onClick={generateImage}
            disabled={isGenerating || !prompt.trim()}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center gap-2"
          >
            {isGenerating ? (
              <>
                <Loader2 size={20} className="animate-spin" />
                {batchCount > 1 ? `Generating ${batchCount} images...` : 'Generating...'}
              </>
            ) : (
              <>
                <Wand2 size={20} />
                {batchCount > 1 ? `Generate ${batchCount} Images` : 'Generate Image'}
              </>
            )}
          </button>

          {/* Status and Progress */}
          {(currentStatus || batchProgress.total > 0) && (
            <div className="space-y-3">
              {batchProgress.total > 1 && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(batchProgress.current / batchProgress.total) * 100}%`
                    }}
                  ></div>
                </div>
              )}
              {currentStatus && (
                <div className="text-center text-sm text-gray-600 bg-gray-50 p-3 rounded-md border">
                  {currentStatus}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Generated Images Gallery */}
      {generatedImages.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
            <ImageIcon size={24} />
            Generated Images ({generatedImages.length})
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {generatedImages.map((image) => (
              <div key={image.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <div className="aspect-square bg-gray-100">
                  <img
                    src={image.url}
                    alt={image.prompt}
                    className="w-full h-full object-cover"
                  />
                </div>

                <div className="p-4 space-y-3">
                  <p className="text-sm text-gray-700 line-clamp-2">
                    <strong>Prompt:</strong> {image.prompt}
                  </p>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p><strong>Model:</strong> {MODELS.find(m => m.value === image.model)?.label || image.model}</p>
                    <p><strong>Aspect Ratio:</strong> {image.aspectRatio}</p>
                    <p><strong>Seed:</strong> {image.seed}</p>
                    {image.hasInputImage && (
                      <p><strong>Type:</strong> Image-to-Image</p>
                    )}
                    {image.batchIndex && (
                      <p><strong>Batch:</strong> #{image.batchIndex}</p>
                    )}
                    <p><strong>Generated:</strong> {image.timestamp}</p>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => setSelectedImage(image)}
                      className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded-md flex items-center justify-center gap-2 text-sm transition-colors"
                    >
                      <ImageIcon size={16} />
                      View
                    </button>
                    <button
                      onClick={() => downloadImage(image.url, `flux-${image.id}.${outputFormat}`)}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-md flex items-center justify-center gap-2 text-sm transition-colors"
                    >
                      <Download size={16} />
                      Download
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={() => setSelectedImage(null)}>
          <div className="relative max-w-4xl max-h-full" onClick={(e) => e.stopPropagation()}>
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-colors z-10"
            >
              <X size={24} />
            </button>
            <img
              src={selectedImage.url}
              alt={selectedImage.prompt}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 rounded-b-lg">
              <p className="text-sm mb-2">
                <strong>Prompt:</strong> {selectedImage.prompt}
              </p>
              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-300">
                  <span className="mr-4"><strong>Model:</strong> {MODELS.find(m => m.value === selectedImage.model)?.label}</span>
                  <span className="mr-4"><strong>Aspect:</strong> {selectedImage.aspectRatio}</span>
                  <span><strong>Seed:</strong> {selectedImage.seed}</span>
                </div>
                <button
                  onClick={() => downloadImage(selectedImage.url, `flux-${selectedImage.id}.${outputFormat}`)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center gap-2 text-sm"
                >
                  <Download size={16} />
                  Download
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
