import axios from 'axios';

const BFL_API_KEY = '9b719d34-b6c8-4c62-a03f-265675686a96';

// Increase body size limit for image uploads
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb', // Increase limit to 50MB for base64 images
    },
  },
};

// Model endpoints
const MODEL_ENDPOINTS = {
  'flux-kontext-pro': 'https://api.bfl.ai/v1/flux-kontext-pro',
  'flux-pro-1.1': 'https://api.bfl.ai/v1/flux-pro-1.1',
  'flux-pro': 'https://api.bfl.ai/v1/flux-pro',
  'flux-dev': 'https://api.bfl.ai/v1/flux-dev'
};

// Function to convert aspect ratio to width/height
function aspectRatioToWidthHeight(aspectRatio) {
  const ratioMap = {
    '3:7': { width: 768, height: 1792 },
    '4:7': { width: 896, height: 1568 },
    '9:16': { width: 768, height: 1344 },
    '2:3': { width: 832, height: 1248 },
    '1:1': { width: 1024, height: 1024 },
    '3:2': { width: 1248, height: 832 },
    '16:9': { width: 1344, height: 768 },
    '7:4': { width: 1568, height: 896 },
    '7:3': { width: 1792, height: 768 }
  };

  return ratioMap[aspectRatio] || { width: 1024, height: 1024 };
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      prompt,
      model = 'flux-kontext-pro',
      input_image = null,
      input_image_2 = null,
      input_image_3 = null,
      input_image_4 = null,
      aspect_ratio = '1:1',
      seed = null,
      prompt_upsampling = false,
      safety_tolerance = 2,
      output_format = 'jpeg'
    } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const apiUrl = MODEL_ENDPOINTS[model];
    if (!apiUrl) {
      return res.status(400).json({ error: 'Invalid model selected' });
    }

    // Create request to BFL API based on model type
    let requestData;

    if (model === 'flux-kontext-pro') {
      // Kontext Pro uses aspect_ratio parameter
      requestData = {
        prompt,
        aspect_ratio,
        prompt_upsampling,
        safety_tolerance,
        output_format
      };

      // Add input images for multi-reference generation (new feature!)
      if (input_image) {
        requestData.input_image = input_image;
      }
      if (input_image_2) {
        requestData.input_image_2 = input_image_2;
      }
      if (input_image_3) {
        requestData.input_image_3 = input_image_3;
      }
      if (input_image_4) {
        requestData.input_image_4 = input_image_4;
      }
    } else {
      // Other models (flux-pro-1.1, flux-pro, flux-dev) use width/height
      const dimensions = aspectRatioToWidthHeight(aspect_ratio);
      requestData = {
        prompt,
        width: dimensions.width,
        height: dimensions.height,
        prompt_upsampling,
        safety_tolerance,
        output_format
      };

      // These models support image_prompt (text description of image style)
      // but NOT input_image (actual image upload)
      if (input_image) {
        console.log(`Warning: ${model} does not support input_image parameter. Use FLUX Kontext Pro for image-to-image generation.`);
      }

      // Add model-specific parameters
      if (model === 'flux-pro') {
        requestData.steps = 40;
        requestData.guidance = 2.5;
        requestData.interval = 2;
      } else if (model === 'flux-dev') {
        requestData.steps = 28;
        requestData.guidance = 3;
      }
    }

    if (seed !== null) {
      requestData.seed = seed;
    }

    // Debug: Log the request data
    console.log('API Request:', {
      url: apiUrl,
      model: model,
      data: requestData,
      aspectRatio: aspect_ratio
    });

    const response = await axios.post(apiUrl, requestData, {
      headers: {
        'accept': 'application/json',
        'x-key': BFL_API_KEY,
        'Content-Type': 'application/json'
      }
    });

    // Debug: Log the response
    console.log('API Response:', response.data);

    const { id, polling_url } = response.data;

    res.status(200).json({
      success: true,
      id,
      polling_url
    });

  } catch (error) {
    console.error('Error creating image generation request:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Failed to create image generation request',
      details: error.response?.data || error.message
    });
  }
}
