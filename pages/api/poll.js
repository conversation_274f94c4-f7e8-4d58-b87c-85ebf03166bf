import axios from 'axios';

const BFL_API_KEY = '9b719d34-b6c8-4c62-a03f-265675686a96';

// API configuration
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { polling_url } = req.query;

    if (!polling_url) {
      return res.status(400).json({ error: 'Polling URL is required' });
    }

    const response = await axios.get(polling_url, {
      headers: {
        'accept': 'application/json',
        'x-key': BFL_API_KEY
      }
    });

    res.status(200).json(response.data);

  } catch (error) {
    console.error('Error polling result:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Failed to poll result',
      details: error.response?.data || error.message
    });
  }
}
