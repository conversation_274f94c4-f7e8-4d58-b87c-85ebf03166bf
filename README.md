# FLUX.1 Kontext Image Generator

Bu proje, Black Forest Labs'ın FLUX.1 Kontext Pro API'sini kullanarak AI ile görsel oluşturma arayüzü sağlar.

## Özellikler

### 🎨 Görsel Oluşturma
- **Text-to-Image**: <PERSON>in açıklamasından görsel oluşturma
- **Image-to-Image**: Referans görsel ile yeni görsel oluşturma
- **Model Seçimi**: 4 farklı FLUX modeli desteği
- **Gerçek zamanlı durum takibi**: Oluşturma sürecini canlı izleme
- **Çoklu format desteği**: JPEG ve PNG çıktı formatları

### ⚙️ Gelişmiş Ayarlar
- **Aspect Ratio**: 3:7'den 7:3'e kadar 9 farklı en-boy oranı
- **Safety Tolerance**: 0-6 arası moderasyon seviyesi ayarı
- **Seed Control**: Tekrarlanabilir sonuçlar için seed değeri
- **Prompt Upsampling**: Prompt kalitesini artırma
- **Output Format**: JPEG/PNG seçimi

### 🖼️ Galeri ve İndirme
- **Görsel galerisi**: Oluşturulan tüm görselleri görüntüleme
- **Metadata görüntüleme**: Model, prompt, seed, batch bilgisi, tarih
- **İndirme özelliği**: Görselleri yerel olarak kaydetme
- **Otomatik kaydetme**: Downloads klasörüne otomatik indirme
- **Referans görsel desteği**: Yüklenen görsellerin önizlemesi

### 📦 Batch Generation
- **Çoklu üretim**: 1-10 arası görsel batch üretimi
- **Progress tracking**: İlerleme çubuğu ve durum takibi
- **Otomatik seed artırma**: Her batch için farklı seed değeri
- **Hata toleransı**: Bir görsel başarısız olsa bile diğerleri devam eder

### 🤖 Model Seçenekleri
- **FLUX.1 Kontext Pro**: Text-to-image ve image editing (referans görsel desteği)
- **FLUX.1.1 Pro**: En yeni yüksek kaliteli text-to-image modeli
- **FLUX.1 Pro**: Yüksek kaliteli text-to-image oluşturma
- **FLUX.1 Dev**: Geliştirme ve test amaçlı model

## Kurulum

1. Bağımlılıkları yükleyin:
```bash
npm install
```

2. Geliştirme sunucusunu başlatın:
```bash
npm run dev
```

3. Tarayıcınızda `http://localhost:3000` adresine gidin.

## API Parametreleri

### Zorunlu Parametreler
- `prompt`: Görsel açıklaması (string)

### Opsiyonel Parametreler
- `model`: Kullanılacak FLUX modeli (varsayılan: "flux-kontext-pro")
- `input_image`: Referans görsel (base64, sadece Kontext modelleri için)
- `aspect_ratio`: En-boy oranı (varsayılan: "1:1")
- `seed`: Tekrarlanabilirlik için seed değeri (varsayılan: null)
- `prompt_upsampling`: Prompt iyileştirme (varsayılan: false)
- `safety_tolerance`: Moderasyon seviyesi 0-6 (varsayılan: 2)
- `output_format`: Çıktı formatı "jpeg" veya "png" (varsayılan: "jpeg")

### Frontend Özellikleri
- `batch_count`: Kaç adet görsel üretileceği (1-10 arası)
- `auto_save`: Otomatik indirme aktif/pasif

## Desteklenen En-Boy Oranları

- **3:7** - Dikey portrait
- **4:7** - Dikey
- **9:16** - Mobil dikey
- **2:3** - Klasik portrait
- **1:1** - Kare (varsayılan)
- **3:2** - Klasik landscape
- **16:9** - Widescreen
- **7:4** - Yatay
- **7:3** - Geniş landscape

## Safety Tolerance Seviyeleri

- **0**: En katı moderasyon
- **1**: Çok katı
- **2**: Katı (varsayılan)
- **3**: Orta
- **4**: İzin verici
- **5**: Daha izin verici
- **6**: En izin verici

## Teknolojiler

- **Next.js 14**: React framework
- **Tailwind CSS**: Styling
- **Lucide React**: İkonlar
- **Axios**: HTTP istekleri
- **FLUX.1 Kontext Pro API**: AI görsel oluşturma

## API Endpoints

### POST /api/generate
Görsel oluşturma isteği başlatır.

### GET /api/poll
Oluşturma durumunu kontrol eder.

## Kullanım

### Text-to-Image (Metin'den Görsel)
1. Model seçin (önerilen: FLUX.1 Kontext Pro)
2. Prompt alanına istediğiniz görselin açıklamasını yazın
3. İsteğe bağlı olarak gelişmiş ayarları düzenleyin
4. "Generate Image" butonuna tıklayın

### Image-to-Image (Görsel'den Görsel)
1. FLUX.1 Kontext Pro modelini seçin
2. "Reference Image" alanına bir görsel yükleyin
3. Prompt alanına değişiklik açıklamasını yazın (örn: "same woman but with red hair")
4. "Generate Image" butonuna tıklayın

### Batch Generation
5. Batch Count'u 1-10 arası seçin (çoklu üretim için)
6. Auto-save'i aktif edin (otomatik indirme için)

### Sonuç
7. Görsel oluşturulurken durum mesajlarını ve progress bar'ı takip edin
8. Oluşturulan görselleri galeri bölümünde görüntüleyin
9. İstediğiniz görseli manuel olarak indirin (auto-save aktifse otomatik iner)

## Örnek Promptlar

### Text-to-Image Promptları
- "A small furry elephant pet looks out from a cat house"
- "Abstract expressionist painting Pop Art and cubism early 20 century, straight lines and solids, cute cat face without body, warm colors"
- "A cute round rusted robot repairing a classic pickup truck, colorful, futuristic, vibrant glow, van gogh style"

### Image-to-Image Promptları (Referans görsel ile)
- "same woman but with red hair"
- "change the car color to blue"
- "make it nighttime with stars in the sky"
- "add sunglasses to the person"
- "replace 'HELLO' with 'WORLD'" (metin değiştirme)

## Lisans

Bu proje kişisel kullanım içindir.
